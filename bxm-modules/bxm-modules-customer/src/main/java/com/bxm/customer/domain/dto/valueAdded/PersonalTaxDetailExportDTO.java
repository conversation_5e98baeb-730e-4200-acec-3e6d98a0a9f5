package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 个税明细导出DTO
 *
 * 用于导出个税明细Excel文件，继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数、
 * 养老、失业、工伤、医疗、生育），并包含个税特有字段：应发工资、公积金个人缴存金额、其他等
 *
 * 注意：重新声明保险字段以覆盖父类的Excel注解，实现"保险"后缀的导出名称
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("个税明细导出DTO")
public class PersonalTaxDetailExportDTO extends BaseDetailExportDTO {

    /** 养老保险 - 重新声明以覆盖父类Excel注解 */
    @Excel(name = "养老保险", sort = 7)
    @ApiModelProperty(value = "养老保险")
    private String yangLao;

    /** 失业保险 - 重新声明以覆盖父类Excel注解 */
    @Excel(name = "失业保险", sort = 8)
    @ApiModelProperty(value = "失业保险")
    private String shiYe;

    /** 工伤保险 - 重新声明以覆盖父类Excel注解 */
    @Excel(name = "工伤保险", sort = 9)
    @ApiModelProperty(value = "工伤保险")
    private String gongShang;

    /** 医疗保险 - 重新声明以覆盖父类Excel注解 */
    @Excel(name = "医疗保险", sort = 10)
    @ApiModelProperty(value = "医疗保险")
    private String yiLiao;

    /** 生育保险 - 重新声明以覆盖父类Excel注解 */
    @Excel(name = "生育保险", sort = 11)
    @ApiModelProperty(value = "生育保险")
    private String shengYu;

    /** 应发工资 */
    @Excel(name = "应发工资", sort = 12)
    @ApiModelProperty(value = "应发工资")
    private String grossSalary;

    /** 公积金个人缴存金额 */
    @Excel(name = "公积金个人缴存金额", sort = 13)
    @ApiModelProperty(value = "公积金个人缴存金额")
    private String housingFundPersonalAmount;

    /** 其他 */
    @Excel(name = "其他", sort = 14)
    @ApiModelProperty(value = "其他")
    private String other;
}
